{/* Bottom Player Cards (Current Player - Always Your Cards) */}
      {playersByPosition.bottom && (
        <SafeAreaView className="absolute bottom-[-1px] left-0 right-0 flex-row justify-center">
          <View className="flex-row">
            {/* Player Name Indicator */}
            <View className="absolute -top-8 left-1/2 transform -translate-x-1/2">
              {getEnhancedPlayerDisplay(playersByPosition.bottom)}
            </View>
            {sortPlayerCards(playersByPosition.bottom.cards).map(
              (card, index) => {
                const isTurnToPlay =
                  gameRoom.gameState === "in_progress" &&
                  gameRoom.currentTurn === playerId;
                const isTrumpSelection =
                  gameRoom.gameState === "trump_type_selection" &&
                  gameRoom.highestBidder === playerId;

                // For regular play, check if this specific card is playable based on suit following rules
                let isPlayable = false;
                if (isTurnToPlay) {
                  const playableCards = getPlayableCards();
                  isPlayable = playableCards.has(card.id);
                } else if (isTrumpSelection) {
                  isPlayable = true; // All cards playable during trump selection
                }

                // Find the original index of this card in the unsorted array for handleCardClick
                const originalIndex = playersByPosition.bottom!.cards.findIndex(
                  (originalCard) => originalCard.id === card.id
                );

                return renderEnhancedCard(
                  card.id,
                  index,
                  playersByPosition.bottom!,
                  "bottom",
                  isPlayable,
                  () => handleCardClick(originalIndex)
                );
              }
            )}
          </View>
        </SafeAreaView>
      )}

      {/* Right Player Cards */}
      {playersByPosition.right && (
        <View className="absolute right-safe inset-y-0 flex flex-col h-full justify-center pr-2">
          <View className="relative">
            <View className="absolute -right-10 top-1/2 -translate-y-1/2 -rotate-90 z-10">
              {getEnhancedPlayerDisplay(playersByPosition.right)}
            </View>
            <View
              className="relative"
              style={{ height: playersByPosition.right.cards.length * 15 + 90 }}
            >
              {playersByPosition.right.cards.map((card, index) => (
                <View
                  key={`right-${index}`}
                  className="absolute"
                  style={{
                    top: index * 15,
                    zIndex: index,
                  }}
                >
                  {renderEnhancedCard(
                    card.id,
                    index,
                    playersByPosition.right!,
                    "right",
                    false
                  )}
                </View>
              ))}
            </View>
          </View>
        </View>
      )}

      {/* Top Player Cards */}
      {playersByPosition.top && (
        <SafeAreaView className="absolute top-[-70px] left-0 right-0 flex-row justify-center">
          <View className="relative">
            {/* Player Name Indicator */}
            <View className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
              {getEnhancedPlayerDisplay(playersByPosition.top)}
            </View>
            <View className="flex-row justify-center items-center">
              {playersByPosition.top.cards.map((card, index) => (
                <View
                  key={`top-${index}`}
                  className="relative"
                  style={{
                    marginLeft: index > 0 ? -45 : 0, // Overlap cards by 45px
                    zIndex: index, // Stack cards properly
                  }}
                >
                  {renderEnhancedCard(
                    card.id,
                    index,
                    playersByPosition.top!,
                    "top",
                    false
                  )}
                </View>
              ))}
            </View>
          </View>
        </SafeAreaView>
      )}

      {/* Left Player Cards */}
      {playersByPosition.left && (
        // <View className="absolute left-[-100px] top-[25%]">
        <View
          className="flex flex-col justify-center h-full"
          // className="absolute"
          style={{
            left: isIPhone() ? -20 : -30, // left-[-100px] = -100px
          }}
        >
          <View className="relative">
            {/* Player Name Indicator */}
            <View className="absolute right-[-165px] top-[50%] rotate-90">
              {getEnhancedPlayerDisplay(playersByPosition.left)}
            </View>
            {/* Container for overlapping cards */}
            <View
              style={{ height: playersByPosition.left.cards.length * 15 + 90 }}
            >
              {playersByPosition.left.cards.map((card, index) => (
                <View
                  key={`left-${index}`}
                  className="absolute"
                  style={{
                    bottom: index * 15,
                    zIndex: index,
                  }}
                >
                  {renderEnhancedCard(
                    card.id,
                    index,
                    playersByPosition.left!,
                    "left",
                    false
                  )}
                </View>
              ))}
            </View>
          </View>
        </View>
      )}