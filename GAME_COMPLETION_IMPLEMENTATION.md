# 304 Card Game - Official Game Completion Logic Implementation

## Overview

This document outlines the implementation of the official 304 card game completion logic based on the authentic rules from [https://www.pagat.com/jass/304.html](https://www.pagat.com/jass/304.html). The implementation replaces the previous point-based system with the official token-based scoring system.

## Key Features Implemented

### 1. **Official Token-Based Scoring System**

#### Token Rules (from official 304 rules):
- Each team starts with **11 tokens** (22 total tokens in the game)
- Game ends when one team collects **all 22 tokens** or the opponent has **0 tokens**
- Token transfers are based on bid outcomes and amounts

#### Token Distribution Rules:
- **Bid < 200**: Win 1 token (success) / Lose 2 tokens (failure)
- **Bid 200-249**: Win 2 tokens (success) / Lose 3 tokens (failure)  
- **Bid 250+**: Win 3 tokens (success) / Lose 4 tokens (failure)
- **Partner Close Caps**: Win 4 tokens (success) / Lose 5 tokens (failure)

#### Special Conditions:
- **Caps Bonus**: +1 token for correct Caps announcement (before 7th trick)
- **Wrong Caps Penalty**: -2 tokens for incorrect Caps announcement
- **Caps Failure Penalty**: -5 tokens for losing trick after Caps announced
- **External Caps Bonus**: +1 token more than normal failed bid

### 2. **Game End Conditions**

The game ends when:
- One team has collected all 22 tokens (`all_tokens_won`)
- One team has 0 tokens remaining (`opponent_eliminated`)
- Manual game termination (`manual_end`)

### 3. **Win Streak Tracking**

- **Consecutive Wins**: Track consecutive round wins for each team
- **Total Rounds Won**: Track total rounds won throughout the game
- **Win Streak Display**: Visual indicators (🔥) for consecutive wins
- **Reset Logic**: Win streaks reset when the other team wins a round

### 4. **Enhanced Game State Management**

#### New Interfaces Added:
```typescript
interface TokenTransfer {
  fromTeam: 'team1' | 'team2';
  toTeam: 'team1' | 'team2';
  amount: number;
  reason: 'successful_bid' | 'failed_bid' | 'caps_bonus' | 'wrong_caps_penalty' | 'caps_failure_penalty';
  bidAmount?: number;
  bidType?: string;
}

interface GameCompletionResult {
  isGameComplete: boolean;
  winnerTeam?: { id: 'team1' | 'team2'; name: string; finalTokens: number; };
  loserTeam?: { id: 'team1' | 'team2'; name: string; finalTokens: number; };
  gameEndReason: 'all_tokens_won' | 'opponent_eliminated' | 'manual_end';
  totalRoundsPlayed: number;
  gameDuration: number;
}
```

#### Enhanced Team Interface:
```typescript
interface Team {
  // Existing fields...
  tokens: number; // Official 304 token count (starts at 11)
  consecutiveWins: number; // Track consecutive round wins
  totalRoundsWon: number; // Total rounds won throughout the game
}
```

### 5. **New UI Components**

#### TokenScoreDisplay Component:
- Displays current token counts for both teams
- Shows win streaks with fire emojis (🔥)
- Progress bar showing token distribution
- Recent token transfer information
- Toggle for detailed statistics

#### GameCompletionDialog Component:
- Displays game winner and final statistics
- Shows game duration and total rounds played
- Options to start new game or view token history
- Automatic display when game ends

#### TokenHistoryDialog Component:
- Complete history of all token transfers
- Detailed information about each transfer reason
- Visual indicators for different transfer types
- Summary of total tokens transferred

### 6. **Updated Game Flow**

#### Round Completion Process:
1. **Score Calculation**: Calculate team scores based on tricks won
2. **Token Transfer**: Determine token transfer based on bid outcome
3. **Win Streak Update**: Update consecutive wins and reset opponent's streak
4. **Game Completion Check**: Check if game should end based on token counts
5. **State Update**: Update game state with new token counts and completion status

#### Game Completion Process:
1. **Winner Determination**: Identify winning team and reason
2. **Statistics Calculation**: Calculate final game statistics
3. **Dialog Display**: Show game completion dialog to all players
4. **History Preservation**: Maintain complete token transfer history
5. **New Game Option**: Provide option to start fresh game

## Implementation Files

### Core Logic:
- `services/gameService.ts` - Updated with token-based scoring logic
  - `calculateTokenTransfer()` - Calculate token transfers based on official rules
  - `checkGameEndCondition()` - Determine if game should end
  - `processScoringDecision()` - Updated to use token-based system

### UI Components:
- `components/TokenScoreDisplay.tsx` - Display token counts and win streaks
- `components/GameCompletionDialog.tsx` - Game completion announcement
- `components/TokenHistoryDialog.tsx` - Token transfer history viewer
- `components/GameBoard.tsx` - Updated to integrate new components

### Testing:
- `test_token_scoring_system.js` - Comprehensive test scenarios

## Backward Compatibility

The implementation maintains backward compatibility by:
- Preserving existing point-based scoring fields for legacy support
- Keeping existing round-based functionality intact
- Adding new token-based features alongside existing systems
- Maintaining existing UI components while adding new ones

## Official Rules Compliance

This implementation follows the official 304 rules from pagat.com:
- ✅ Token-based scoring system (11 tokens per team)
- ✅ Correct token distribution based on bid amounts
- ✅ Special condition handling (Caps, Wrong Caps, etc.)
- ✅ Game end conditions (all tokens won or opponent eliminated)
- ✅ Authentic 304 gameplay experience

## Usage

The new token-based system is automatically integrated into the existing game flow. Players will see:
- Token counts displayed prominently in the game UI
- Win streak indicators for consecutive victories
- Game completion dialog when the game ends
- Option to view detailed token transfer history
- Seamless transition between rounds with updated token counts

## Future Enhancements

Potential future improvements:
- Enhanced special condition detection (automatic Caps recognition)
- Advanced statistics and analytics
- Tournament mode with multiple games
- Player performance tracking across games
- Export game history functionality
