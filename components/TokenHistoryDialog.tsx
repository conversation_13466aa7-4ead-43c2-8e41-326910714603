import React from 'react';
import { Mo<PERSON>, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { TokenTransfer } from '../services/gameService';

interface TokenHistoryDialogProps {
  visible: boolean;
  tokenHistory: TokenTransfer[];
  onClose: () => void;
}

export default function TokenHistoryDialog({
  visible,
  tokenHistory,
  onClose
}: TokenHistoryDialogProps) {

  const getReasonIcon = (reason: TokenTransfer['reason']): string => {
    switch (reason) {
      case 'successful_bid':
        return '✅';
      case 'failed_bid':
        return '❌';
      case 'caps_bonus':
        return '🎯';
      case 'wrong_caps_penalty':
        return '⚠️';
      case 'caps_failure_penalty':
        return '💥';
      default:
        return '🔄';
    }
  };

  const getReasonText = (reason: TokenTransfer['reason']): string => {
    switch (reason) {
      case 'successful_bid':
        return 'Successful Bid';
      case 'failed_bid':
        return 'Failed Bid';
      case 'caps_bonus':
        return 'Caps Bonus';
      case 'wrong_caps_penalty':
        return 'Wrong Caps Penalty';
      case 'caps_failure_penalty':
        return 'Caps Failure Penalty';
      default:
        return 'Token Transfer';
    }
  };

  const getReasonColor = (reason: TokenTransfer['reason']): string => {
    switch (reason) {
      case 'successful_bid':
        return 'border-green-500 bg-green-900/20';
      case 'failed_bid':
        return 'border-red-500 bg-red-900/20';
      case 'caps_bonus':
        return 'border-yellow-500 bg-yellow-900/20';
      case 'wrong_caps_penalty':
      case 'caps_failure_penalty':
        return 'border-orange-500 bg-orange-900/20';
      default:
        return 'border-gray-500 bg-gray-900/20';
    }
  };

  const getTeamName = (teamId: 'team1' | 'team2'): string => {
    return teamId === 'team1' ? 'North-South' : 'East-West';
  };

  const getTeamColor = (teamId: 'team1' | 'team2'): string => {
    return teamId === 'team1' ? 'text-blue-300' : 'text-red-300';
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/70 justify-center items-center p-4">
        <View className="bg-gray-800 rounded-xl w-full max-w-md max-h-[80%] border border-gray-600">
          {/* Header */}
          <View className="p-4 border-b border-gray-600">
            <View className="flex-row justify-between items-center">
              <Text className="text-xl font-bold text-white">
                Token Transfer History
              </Text>
              <TouchableOpacity
                onPress={onClose}
                className="bg-gray-600 px-3 py-1 rounded"
              >
                <Text className="text-white">Close</Text>
              </TouchableOpacity>
            </View>
            <Text className="text-gray-300 text-sm mt-1">
              {tokenHistory.length} total transfers
            </Text>
          </View>

          {/* History List */}
          <ScrollView className="flex-1 p-4">
            {tokenHistory.length === 0 ? (
              <View className="items-center py-8">
                <Text className="text-gray-400 text-center">
                  No token transfers yet.{'\n'}
                  Complete a round to see transfer history.
                </Text>
              </View>
            ) : (
              <View className="space-y-3">
                {tokenHistory.map((transfer, index) => (
                  <View
                    key={index}
                    className={`rounded-lg p-3 border ${getReasonColor(transfer.reason)}`}
                  >
                    {/* Transfer Header */}
                    <View className="flex-row items-center justify-between mb-2">
                      <View className="flex-row items-center">
                        <Text className="text-lg mr-2">
                          {getReasonIcon(transfer.reason)}
                        </Text>
                        <Text className="text-white font-semibold">
                          {transfer.amount} tokens
                        </Text>
                      </View>
                      <Text className="text-xs text-gray-400">
                        Round {index + 1}
                      </Text>
                    </View>

                    {/* Transfer Details */}
                    <View className="space-y-1">
                      <Text className="text-sm text-gray-300">
                        {getReasonText(transfer.reason)}
                      </Text>
                      
                      <View className="flex-row items-center">
                        <Text className={`text-sm ${getTeamColor(transfer.fromTeam)}`}>
                          {getTeamName(transfer.fromTeam)}
                        </Text>
                        <Text className="text-gray-400 mx-2">→</Text>
                        <Text className={`text-sm ${getTeamColor(transfer.toTeam)}`}>
                          {getTeamName(transfer.toTeam)}
                        </Text>
                      </View>

                      {transfer.bidAmount && (
                        <Text className="text-xs text-gray-400">
                          Bid Amount: {transfer.bidAmount}
                        </Text>
                      )}

                      {transfer.bidType && transfer.bidType !== 'standard' && (
                        <Text className="text-xs text-yellow-400">
                          Special Bid: {transfer.bidType}
                        </Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>
            )}
          </ScrollView>

          {/* Summary Footer */}
          {tokenHistory.length > 0 && (
            <View className="p-4 border-t border-gray-600 bg-gray-700/50">
              <Text className="text-sm text-gray-300 text-center">
                Total Tokens Transferred: {' '}
                <Text className="text-white font-semibold">
                  {tokenHistory.reduce((sum, transfer) => sum + transfer.amount, 0)}
                </Text>
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}
