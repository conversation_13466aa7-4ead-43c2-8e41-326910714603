import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { Partnership, Player, TokenTransfer } from '../services/gameService';

interface TokenScoreDisplayProps {
  partnership: Partnership;
  players?: Player[];
  recentTokenTransfer?: TokenTransfer;
  showDetails?: boolean;
  onToggleDetails?: () => void;
}

export default function TokenScoreDisplay({
  partnership,
  players = [],
  recentTokenTransfer,
  showDetails = false,
  onToggleDetails
}: TokenScoreDisplayProps) {

  const getWinStreakDisplay = (consecutiveWins: number): string => {
    if (consecutiveWins === 0) return '';
    if (consecutiveWins === 1) return '🔥';
    if (consecutiveWins === 2) return '🔥🔥';
    if (consecutiveWins >= 3) return '🔥🔥🔥';
    return '';
  };

  const getTokenTransferIcon = (reason: TokenTransfer['reason']): string => {
    switch (reason) {
      case 'successful_bid':
        return '✅';
      case 'failed_bid':
        return '❌';
      case 'caps_bonus':
        return '🎯';
      case 'wrong_caps_penalty':
        return '⚠️';
      case 'caps_failure_penalty':
        return '💥';
      default:
        return '🔄';
    }
  };

  const getTokenTransferText = (reason: TokenTransfer['reason']): string => {
    switch (reason) {
      case 'successful_bid':
        return 'Bid Made';
      case 'failed_bid':
        return 'Bid Failed';
      case 'caps_bonus':
        return 'Caps Bonus';
      case 'wrong_caps_penalty':
        return 'Wrong Caps';
      case 'caps_failure_penalty':
        return 'Caps Failed';
      default:
        return 'Transfer';
    }
  };

  const getTeamPlayerNames = (playerIds: string[]): string => {
    if (!players || players.length === 0) {
      return 'Loading...';
    }
    
    const names = playerIds
      .map(id => players.find(p => p.id === id)?.name)
      .filter(Boolean);
    
    return names.length > 0 ? names.join(" & ") : 'No players';
  };

  return (
    <View className="mx-4 mb-1">
      {/* Minimal Two-Column Layout */}
      <View className="flex-row rounded-lg gap-x-64 p-2">
        
        {/* North-South - Left Column */}
        <View className="flex-1 rounded-xl px-3 py-2 bg-gray-800/90">
          {/* Team Header with Enhanced Styling */}
          <View className="flex-row items-center justify-center mb-2">
            <View className="w-2 h-2 bg-blue-400 rounded-full mr-2"></View>
            <Text className="text-blue-200 font-bold text-center text-sm tracking-wide">
              {partnership.team1.name}
            </Text>
            <View className="w-2 h-2 bg-blue-400 rounded-full ml-2"></View>
          </View>
          
          <View className="flex-row items-center justify-between">
            {/* Enhanced Tokens Display */}
            <View className="items-center bg-blue-800/40 rounded-lg px-2 py-1 border border-blue-500/30">
              <Text className="text-2xl font-black text-blue-100 tracking-tight">
                {partnership.team1.tokens}
              </Text>
              <Text className="text-xs text-blue-300 font-medium uppercase tracking-wider">
                tokens
              </Text>
            </View>
            
            {/* Enhanced Details Section */}
            <View className="flex-1 ml-3">
              {/* Player Names with Icon */}
              <View className="flex-row items-center mb-1">
                <Text className="text-xs text-blue-400 mr-1">👥</Text>
                <Text className="text-xs text-blue-100 font-medium flex-1" numberOfLines={1}>
                  {getTeamPlayerNames(partnership.team1.playerIds)}
                </Text>
              </View>
              
              {/* Score and Tricks with Enhanced Layout */}
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-xs text-blue-400 mr-1">🎯</Text>
                  <Text className="text-xs text-blue-200 font-semibold">
                    {partnership.team1.score}
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Text className="text-xs text-blue-400 mr-1">🃏</Text>
                  <Text className="text-xs text-blue-200 font-semibold">
                    {partnership.team1.tricksWon}
                  </Text>
                </View>
              </View>
              
              {/* Win Streak if exists */}
              {partnership.team1.consecutiveWins > 0 && (
                <View className="flex-row items-center justify-center mt-1">
                  <Text className="text-xs text-yellow-300">
                    {getWinStreakDisplay(partnership.team1.consecutiveWins)}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* East-West - Right Column */}
        <View className="flex-1 rounded-xl px-3 py-2 bg-gray-800/90">
          {/* Team Header with Enhanced Styling */}
          <View className="flex-row items-center justify-center mb-2">
            <View className="w-2 h-2 bg-red-400 rounded-full mr-2"></View>
            <Text className="text-red-200 font-bold text-center text-sm tracking-wide">
              {partnership.team2.name}
            </Text>
            <View className="w-2 h-2 bg-red-400 rounded-full ml-2"></View>
          </View>
          
          <View className="flex-row items-center justify-between">
            {/* Enhanced Tokens Display */}
            <View className="items-center bg-red-800/40 rounded-lg px-2 py-1 border border-red-500/30">
              <Text className="text-2xl font-black text-red-100 tracking-tight">
                {partnership.team2.tokens}
              </Text>
              <Text className="text-xs text-red-300 font-medium uppercase tracking-wider">
                tokens
              </Text>
            </View>
            
            {/* Enhanced Details Section */}
            <View className="flex-1 ml-3">
              {/* Player Names with Icon */}
              <View className="flex-row items-center mb-1">
                <Text className="text-xs text-red-400 mr-1">👥</Text>
                <Text className="text-xs text-red-100 font-medium flex-1" numberOfLines={1}>
                  {getTeamPlayerNames(partnership.team2.playerIds)}
                </Text>
              </View>
              
              {/* Score and Tricks with Enhanced Layout */}
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-xs text-red-400 mr-1">🎯</Text>
                  <Text className="text-xs text-red-200 font-semibold">
                    {partnership.team2.score}
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Text className="text-xs text-red-400 mr-1">🃏</Text>
                  <Text className="text-xs text-red-200 font-semibold">
                    {partnership.team2.tricksWon}
                  </Text>
                </View>
              </View>
              
              {/* Win Streak if exists */}
              {partnership.team2.consecutiveWins > 0 && (
                <View className="flex-row items-center justify-center mt-1">
                  <Text className="text-xs text-yellow-300">
                    {getWinStreakDisplay(partnership.team2.consecutiveWins)}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Details Button - Positioned absolutely in top right */}
        {onToggleDetails && (
          <TouchableOpacity
            onPress={onToggleDetails}
            className="absolute top-1 right-1 bg-blue-600 px-1 py-0.5 rounded"
          >
            <Text className="text-white text-xs">
              {showDetails ? 'Hide' : 'Details'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Recent Token Transfer - Compact Display */}
      {recentTokenTransfer && (
        <View className="bg-yellow-900/30 rounded px-2 py-1 border border-yellow-600/50 mt-1">
          <View className="flex-row items-center justify-center">
            <Text className="text-sm mr-1">
              {getTokenTransferIcon(recentTokenTransfer.reason)}
            </Text>
            <Text className="text-yellow-300 text-xs">
              {recentTokenTransfer.amount} • {getTokenTransferText(recentTokenTransfer.reason)}
            </Text>
          </View>
        </View>
      )}

      {/* Detailed Statistics - Only show when requested */}
      {showDetails && (
        <View className="bg-gray-700/50 rounded-lg p-2 mt-2">
          <Text className="text-white font-semibold text-center text-xs mb-2">
            Detailed Statistics
          </Text>

          <View className="flex-row justify-between mb-1">
            <Text className="text-gray-300 text-xs">Team 1 Wins:</Text>
            <Text className="text-white text-xs font-semibold">
              {partnership.team1.totalRoundsWon}
            </Text>
          </View>

          <View className="flex-row justify-between mb-1">
            <Text className="text-gray-300 text-xs">Team 2 Wins:</Text>
            <Text className="text-white text-xs font-semibold">
              {partnership.team2.totalRoundsWon}
            </Text>
          </View>

          <View className="flex-row justify-between mb-1">
            <Text className="text-gray-300 text-xs">Legacy Score 1:</Text>
            <Text className="text-white text-xs">
              {partnership.team1.score}
            </Text>
          </View>

          <View className="flex-row justify-between">
            <Text className="text-gray-300 text-xs">Legacy Score 2:</Text>
            <Text className="text-white text-xs">
              {partnership.team2.score}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
}
