import React from 'react';
import { Modal, Text, TouchableOpacity, View, Alert } from 'react-native';
import { GameCompletionResult, TokenTransfer } from '../services/gameService';

interface GameCompletionDialogProps {
  visible: boolean;
  gameCompletion: GameCompletionResult;
  tokenHistory?: TokenTransfer[];
  onNewGame: () => void;
  onViewHistory: () => void;
  onClose: () => void;
}

export default function GameCompletionDialog({
  visible,
  gameCompletion,
  tokenHistory = [],
  onNewGame,
  onViewHistory,
  onClose
}: GameCompletionDialogProps) {
  
  const formatDuration = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const getEndReasonText = (reason: GameCompletionResult['gameEndReason']): string => {
    switch (reason) {
      case 'all_tokens_won':
        return 'Won all tokens!';
      case 'opponent_eliminated':
        return 'Opponent eliminated!';
      case 'manual_end':
        return 'Game ended manually';
      default:
        return 'Game completed';
    }
  };

  const handleNewGame = () => {
    Alert.alert(
      'Start New Game',
      'Are you sure you want to start a new game? This will reset all scores and tokens.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'New Game', onPress: onNewGame }
      ]
    );
  };

  if (!gameCompletion.isGameComplete) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/70 justify-center items-center p-4">
        <View className="bg-gray-800 rounded-xl p-6 w-full max-w-md border-2 border-yellow-500">
          {/* Header */}
          <View className="items-center mb-6">
            <Text className="text-3xl font-bold text-yellow-400 mb-2">
              🏆 GAME COMPLETE! 🏆
            </Text>
            <Text className="text-lg text-gray-300 text-center">
              {getEndReasonText(gameCompletion.gameEndReason)}
            </Text>
          </View>

          {/* Winner Section */}
          {gameCompletion.winnerTeam && (
            <View className="bg-green-900/50 rounded-lg p-4 mb-4 border border-green-500">
              <Text className="text-xl font-bold text-green-400 text-center mb-2">
                🎉 WINNER 🎉
              </Text>
              <Text className="text-lg text-white text-center font-semibold">
                {gameCompletion.winnerTeam.name}
              </Text>
              <Text className="text-sm text-green-300 text-center">
                Final Tokens: {gameCompletion.winnerTeam.finalTokens}
              </Text>
            </View>
          )}

          {/* Game Statistics */}
          <View className="bg-gray-700 rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold text-white mb-3 text-center">
              Game Statistics
            </Text>
            
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-300">Total Rounds:</Text>
                <Text className="text-white font-semibold">
                  {gameCompletion.totalRoundsPlayed}
                </Text>
              </View>
              
              <View className="flex-row justify-between">
                <Text className="text-gray-300">Game Duration:</Text>
                <Text className="text-white font-semibold">
                  {formatDuration(gameCompletion.gameDuration)}
                </Text>
              </View>

              {gameCompletion.loserTeam && (
                <View className="flex-row justify-between">
                  <Text className="text-gray-300">Final Score:</Text>
                  <Text className="text-white font-semibold">
                    {gameCompletion.winnerTeam?.finalTokens} - {gameCompletion.loserTeam.finalTokens}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Token Transfer Summary */}
          {tokenHistory.length > 0 && (
            <View className="bg-blue-900/30 rounded-lg p-3 mb-6 border border-blue-500">
              <Text className="text-sm font-semibold text-blue-300 text-center mb-2">
                Total Token Transfers: {tokenHistory.length}
              </Text>
              <Text className="text-xs text-blue-200 text-center">
                Tap "View History" to see detailed transfer log
              </Text>
            </View>
          )}

          {/* Action Buttons */}
          <View className="space-y-3">
            <TouchableOpacity
              onPress={handleNewGame}
              className="bg-green-600 py-3 px-6 rounded-lg"
            >
              <Text className="text-white font-semibold text-center text-lg">
                🎮 Start New Game
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={onViewHistory}
              className="bg-blue-600 py-3 px-6 rounded-lg"
            >
              <Text className="text-white font-semibold text-center">
                📊 View Game History
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={onClose}
              className="bg-gray-600 py-2 px-6 rounded-lg"
            >
              <Text className="text-white text-center">
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
