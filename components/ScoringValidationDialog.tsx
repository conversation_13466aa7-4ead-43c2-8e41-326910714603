import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { ScoreValidationResult } from '../services/gameService';

interface ScoringValidationDialogProps {
  visible: boolean;
  scoreValidation: ScoreValidationResult;
  onAutoNextRound: () => void;
  pendingDecision?: boolean; // Whether a decision is still pending
}

export default function ScoringValidationDialog({
  visible,
  scoreValidation,
  onAutoNextRound,
  pendingDecision = true
}: ScoringValidationDialogProps) {
  const [countdown, setCountdown] = useState(10);

  console.log('ScoringValidationDialog render - visible:', visible, 'pendingDecision:', pendingDecision);

  // Auto-progression timer
  useEffect(() => {
    if (!visible || !pendingDecision) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // Time's up - automatically proceed to next round
          onAutoNextRound();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [visible, pendingDecision, onAutoNextRound]);

  // Reset countdown when dialog becomes visible
  useEffect(() => {
    if (visible && pendingDecision) {
      setCountdown(10);
    }
  }, [visible, pendingDecision]);

  // Don't render if not visible
  if (!visible) {
    return null;
  }

  const { bidderTeam, bidAmount, bidderTeamScore, excessPoints } = scoreValidation;
  const isDecisionMade = !pendingDecision;

  return (
    <View style={styles.compactOverlay}>
      <View style={styles.compactContainer}>
        {/* Left Section - Score Summary */}
        <View style={styles.leftSection}>
          <View style={styles.headerRow}>
            <Text style={styles.headerText}>🎯 Bid Exceeded</Text>
            <Text style={styles.teamName}>{bidderTeam.name}</Text>
          </View>
          <View style={styles.scoreRow}>
            <Text style={styles.scoreLabel}>Bid: {bidAmount}</Text>
            <Text style={styles.scoreLabel}>Score: <Text style={styles.successText}>{bidderTeamScore}</Text></Text>
            <Text style={styles.bonusText}>+{excessPoints}</Text>
          </View>
        </View>

        {/* Right Section - Status & Timer */}
        <View style={styles.rightSection}>
          <Text style={styles.statusText}>
            {isDecisionMade ? "🔄 Starting..." : "✅ Auto-next round"}
          </Text>
          {pendingDecision && (
            <Text style={styles.countdownText}>
              {countdown}s
            </Text>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  compactOverlay: {
    position: 'absolute',
    top: 60, // Position below the top player area
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: 12,
    zIndex: 1000,
    elevation: 20,
  },
  compactContainer: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    flex: 1,
    marginRight: 16,
  },
  rightSection: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerText: {
    color: '#10b981',
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 8,
  },
  teamName: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  scoreRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  scoreLabel: {
    color: '#9ca3af',
    fontSize: 12,
  },
  successText: {
    color: '#10b981',
    fontWeight: '600',
  },
  bonusText: {
    color: '#f59e0b',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusText: {
    color: '#d1fae5',
    fontSize: 11,
    textAlign: 'center',
    marginBottom: 4,
  },
  countdownText: {
    color: '#fbbf24',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
