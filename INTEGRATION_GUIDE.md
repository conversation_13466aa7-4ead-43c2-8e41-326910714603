# 304 Card Game - Token-Based Scoring Integration Guide

## Quick Start

The new official 304 token-based scoring system has been successfully integrated into your existing codebase. Here's what you need to know:

## ✅ What's Been Implemented

### 1. **Core Token System**
- **Official 304 Rules**: Based on authentic rules from https://www.pagat.com/jass/304.html
- **Token Distribution**: Each team starts with 11 tokens (22 total)
- **Game End Condition**: First team to collect all 22 tokens wins
- **Backward Compatible**: Existing point-based system preserved for compatibility

### 2. **Enhanced Game State**
```typescript
// New fields added to Team interface
interface Team {
  tokens: number;              // Official 304 token count
  consecutiveWins: number;     // Win streak tracking
  totalRoundsWon: number;      // Total rounds won
}

// New game completion tracking
interface GameCompletionResult {
  isGameComplete: boolean;
  winnerTeam?: TeamInfo;
  gameEndReason: string;
  totalRoundsPlayed: number;
  gameDuration: number;
}
```

### 3. **Token Transfer Logic**
- **Bid-Based Transfers**: Tokens transferred based on bid amount and success
- **Special Conditions**: Caps bonuses, Wrong Caps penalties, etc.
- **Complete History**: All token transfers tracked and displayable

### 4. **New UI Components**
- **TokenScoreDisplay**: Shows current token counts and win streaks
- **GameCompletionDialog**: Displays game winner and statistics
- **TokenHistoryDialog**: Complete transfer history viewer

## 🚀 How to Use

### For Players
1. **Token Display**: Token counts are prominently displayed at the top of the game board
2. **Win Streaks**: Fire emojis (🔥) indicate consecutive wins
3. **Game Completion**: Automatic dialog when game ends with winner announcement
4. **History**: View complete token transfer history anytime

### For Developers
1. **Automatic Integration**: Token system works alongside existing game flow
2. **No Breaking Changes**: All existing functionality preserved
3. **Enhanced Data**: Additional game statistics and completion tracking
4. **Easy Customization**: Modular components for UI customization

## 📊 Token Distribution Rules

| Bid Amount | Success | Failure |
|------------|---------|---------|
| < 200      | +1 token | -2 tokens |
| 200-249    | +2 tokens | -3 tokens |
| 250+       | +3 tokens | -4 tokens |
| Partner Close Caps | +4 tokens | -5 tokens |

### Special Conditions
- **Caps Bonus**: +1 token for correct Caps (before 7th trick)
- **Wrong Caps**: -2 tokens penalty
- **Caps Failure**: -5 tokens for losing trick after Caps
- **External Caps**: +1 token more than normal failure

## 🔧 Configuration

### Game Settings
```typescript
// Token rules are configurable via TOKEN_RULES constant
const TOKEN_RULES = {
  INITIAL_TOKENS_PER_TEAM: 11,
  TOTAL_TOKENS: 22,
  // ... other rules
};
```

### UI Customization
```typescript
// TokenScoreDisplay props
<TokenScoreDisplay
  partnership={partnership}
  recentTokenTransfer={lastTransfer}
  showDetails={showDetails}
  onToggleDetails={handleToggle}
/>
```

## 🎮 Game Flow Integration

### Round Completion
1. **Score Calculation**: Team scores calculated from tricks won
2. **Token Transfer**: Tokens transferred based on bid outcome
3. **Win Streak Update**: Consecutive wins tracked and displayed
4. **Game Check**: Automatic check for game completion
5. **State Update**: All game state updated atomically

### Game Completion
1. **Winner Detection**: Automatic detection when team reaches 22 tokens
2. **Statistics**: Complete game statistics calculated
3. **Dialog Display**: Game completion dialog shown to all players
4. **New Game**: Option to start fresh game with reset tokens

## 🧪 Testing

### Manual Testing Scenarios
1. **Basic Token Transfer**: Play rounds and verify token transfers
2. **Win Streaks**: Win consecutive rounds and check fire emoji display
3. **Game Completion**: Play until one team has all tokens
4. **History Viewing**: Check token transfer history dialog
5. **New Game**: Start new game and verify token reset

### Validation Functions
```typescript
// Helper functions for validation
validateTokenCounts(partnership);     // Ensures total = 22
getTokenTransferDescription(transfer); // Human-readable descriptions
formatWinStreak(consecutiveWins);     // Win streak display
```

## 🔍 Debugging

### Common Issues
1. **Token Count Mismatch**: Use `validateTokenCounts()` to check
2. **Missing Transfers**: Check `tokenTransferHistory` array
3. **UI Not Updating**: Verify component props and state updates
4. **Game Not Ending**: Check `checkGameEndCondition()` logic

### Debug Information
```typescript
// Available in game state
gameRoom.tokenTransferHistory    // All transfers
gameRoom.gameCompletion         // Completion status
partnership.team1.tokens        // Current token counts
partnership.team1.consecutiveWins // Win streaks
```

## 📱 Mobile Considerations

### UI Responsiveness
- Token display optimized for mobile screens
- Touch-friendly dialogs and buttons
- Responsive layout for different screen sizes
- Accessibility features included

### Performance
- Efficient state updates
- Minimal re-renders
- Optimized animations
- Memory-conscious history tracking

## 🔄 Migration Notes

### From Point-Based System
- **No Data Loss**: Existing games continue with point system
- **New Games**: Automatically use token system
- **Gradual Transition**: Both systems coexist during migration
- **User Education**: Clear indicators of new scoring system

### Database Updates
- **New Fields**: Added to existing Team and GameRoom schemas
- **Backward Compatible**: Old games still function normally
- **Migration Scripts**: Available if needed for data conversion

## 🎯 Best Practices

### For Game Hosts
1. **Explain System**: Brief players on new token-based scoring
2. **Show Features**: Demonstrate win streak and history features
3. **Monitor Games**: Use completion statistics for game analysis

### For Players
1. **Watch Tokens**: Keep track of token counts during play
2. **Understand Stakes**: Higher bids = more tokens at risk
3. **Use History**: Review transfer history to improve strategy

## 🚨 Important Notes

### Game Balance
- **Official Rules**: Follows authentic 304 scoring exactly
- **Fair Play**: Token system prevents score manipulation
- **Strategic Depth**: Adds strategic consideration to bidding

### Data Integrity
- **Atomic Updates**: All token transfers are atomic operations
- **Validation**: Built-in validation prevents invalid states
- **History**: Complete audit trail of all token movements

## 📞 Support

### Documentation
- `GAME_COMPLETION_IMPLEMENTATION.md` - Technical details
- `test_token_scoring_system.js` - Test scenarios
- Component JSDoc comments - API documentation

### Troubleshooting
1. Check browser console for error messages
2. Verify game state in Firebase console
3. Use debug helper functions for validation
4. Review token transfer history for anomalies

## 🎉 Conclusion

The official 304 token-based scoring system is now fully integrated and ready for use. The implementation maintains backward compatibility while providing an authentic 304 gaming experience based on official rules.

Key benefits:
- ✅ Authentic 304 gameplay experience
- ✅ Enhanced game completion tracking
- ✅ Win streak visualization
- ✅ Complete game history
- ✅ Mobile-optimized UI
- ✅ Backward compatibility

Enjoy the enhanced 304 card game experience!
